#!/usr/bin/env python3
"""
Kompletní skript pro zpracování dokumentů - od OCR po uložení výsledků.

Tento skript provádí:
1. OCR zpracování dokumentu
2. Zpracování a čištění textů
3. Klasifika<PERSON> klíčů (semantic classifier)
4. Postprocessing klasifikace
5. GAT klasifikaci (volitelně)
6. Interaktivní zobrazení pro ruční dokončení
7. Uložení výsledků do složky Results

Použití:
    python process_document.py <cesta_k_pdf>
    python process_document.py Data/dokument.pdf
"""

import sys
import os
import argparse
from pathlib import Path

# Import modulů z jednotlivých složek
from OCR import ocr
from utils import utils
from semantic_classifier import key_classify
from preview import preview_qt as preview
from GAT.GATClassifier import GatNodeClassifier
from GAT.prepare_graph_data import prepare_graph_data_from_dataframe
from GAT.train_gat import prepare_spatial_features, prepare_class_features


def process_document(file_path, use_gat=False, interactive=True):
    """
    Kompletní zpracování dokumentu.
    
    Args:
        file_path (str): Cesta k PDF dokumentu
        use_gat (bool): Zda použít GAT klasifikaci
        interactive (bool): Zda zobrazit interaktivní viewer
        
    Returns:
        pd.DataFrame: Zpracovaný DataFrame s výsledky
    """
    
    print("🚀 Zpracování dokumentu")
    print("=" * 60)
    print(f"📄 Soubor: {file_path}")
    print(f"🧠 GAT klasifikace: {'Zapnuta' if use_gat else 'Vypnuta'}")
    print(f"🖥️  Interaktivní režim: {'Zapnut' if interactive else 'Vypnut'}")
    print()
    
    # Kontrola existence souboru
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Soubor {file_path} neexistuje")
    
    # Step 1: OCR Processing
    print("1. 🔍 OCR zpracování...")
    df = ocr.do(file_path)
    print(f"   ✓ Nalezeno {len(df)} textových prvků")
    
    # Step 2: Text Processing
    print("\n2. 📝 Zpracování textů...")
    print("   Slučování textů...")
    df = utils.merge_texts(df)
    print(f"   ✓ Po sloučení: {len(df)} prvků")
    
    print("   Klasifikace hodnot...")
    utils.classify_batch_values(df)
    value_classified = len(df[df['value_class'] > 0])
    print(f"   ✓ Klasifikováno {value_classified}/{len(df)} hodnot")
    
    print("   Čištění textů...")
    df = utils.clean_texts(df)
    print("   ✓ Texty vyčištěny")
    
    # Step 3: Key Classification
    print("\n3. 🔑 Klasifikace klíčů...")
    df = key_classify.do(df)
    key_classified = len(df[df['key_class'] > 0])
    print(f"   ✓ Klasifikováno {key_classified}/{len(df)} klíčů")
    
    # Step 4: Postprocessing
    print("\n4. ⚙️  Postprocessing...")
    print("   Aplikace pravidel pro omezení výskytů...")
    df = utils.postprocess(df)
    
    print("   Odstraňování nevalidních textů...")
    df = utils.remove_invalid_texts(df)
    final_count = len(df)
    print(f"   ✓ Finální počet prvků: {final_count}")

    df['source_file'] = file_path

    # Step 5: GAT Classification (volitelně)
    if use_gat:
        print("\n5. 🧠 GAT klasifikace...")
        try:
            # Příprava dat pro GAT
            print("   Příprava prostorových features...")
            df_gat = prepare_spatial_features(df.copy())
            
            print("   Příprava class features...")
            df_gat, feature_names = prepare_class_features(df_gat)
            
            # Inicializace GAT modelu
            node_input_dim = 38  # 6 prostorových + 32 one-hot (9 value + 23 key)
            edge_input_dim = 2   # dx, dy
            output_dim = 19      # Počet result_classes (0-18)

            print(f"   GAT model konfigurace: node_input_dim={node_input_dim}, edge_input_dim={edge_input_dim}, output_dim={output_dim}")
            print(f"   ℹ️  GAT predikce budou uloženy přímo do result_class sloupce")

            classifier = GatNodeClassifier(
                node_in_dim=node_input_dim,
                edge_in_dim=edge_input_dim,
                hidden_dim=64,
                heads=4,
                output_dim=output_dim,
                num_layers=1  # Musí odpovídat natrénovanému modelu (train_gat_new.py používá výchozí hodnotu 1)
            )
            
            # Načtení natrénovaného modelu
            model_path = "GAT/trained_gat_model_new.pth"
            if os.path.exists(model_path):
                print(f"   Načítání modelu z: {model_path}")
                try:
                    import torch
                    classifier.load_state_dict(torch.load(model_path, map_location='cpu'))
                    classifier.eval()
                    print("   ✓ GAT model úspěšně načten")
                except Exception as load_error:
                    print(f"   ⚠️  Chyba při načítání modelu: {load_error}")
                    print(f"   ⚠️  Model byl pravděpodobně natrénován s jiným počtem features")
                    print(f"   ⚠️  Aktuální: {actual_input_dim} features, očekáváno: pravděpodobně 38")
                    print("   ⚠️  Přeskakuji GAT predikci...")
                    raise load_error

                # GAT predikce
                print("   Spouštím GAT predikci...")
                try:
                    import torch
                    import numpy as np

                    # Připravíme data pomocí nové metody (automaticky detekuje formát)
                    device = torch.device('cpu')  # Použijeme CPU pro predikci
                    node_features, edge_features, adj, _, _ = prepare_graph_data_from_dataframe(
                        df_gat, device, is_training=False
                    )

                    # Provedeme predikci pomocí forward metody
                    with torch.no_grad():
                        outputs = classifier(node_features, edge_features, adj)  # [1, N, output_dim]
                        predictions = torch.argmax(outputs, dim=-1).squeeze(0).numpy()  # [N]

                    # Aplikujeme predikce na DataFrame
                    print("   Aplikuji GAT predikce na data...")
                    gat_predictions_applied = 0

                    # Ujistíme se, že existuje sloupec result_class
                    if 'result_class' not in df.columns:
                        df['result_class'] = 0

                    for i, pred_class in enumerate(predictions):
                        if i < len(df):
                            # GAT predikce ukládáme přímo do result_class pro prvky s value_class > 0
                            # (ty budou mít dropdown v interaktivním vieweru)
                            if pred_class > 0 and df.iloc[i]['value_class'] > 0:
                                df.iloc[i, df.columns.get_loc('result_class')] = int(pred_class)
                                gat_predictions_applied += 1

                    print(f"   ✓ GAT predikce dokončena: {gat_predictions_applied} nových klasifikací")

                    # Statistiky GAT predikce
                    unique_preds, counts = np.unique(predictions, return_counts=True)
                    print("   📊 GAT predikce podle tříd:")
                    for pred_class, count in zip(unique_preds, counts):
                        if pred_class > 0:  # Nezobrazujeme třídu 0 (Ostatní)
                            class_name = utils.get_result_class_name(pred_class)
                            print(f"      {pred_class} ({class_name}): {count} prvků")

                except Exception as pred_error:
                    print(f"   ❌ Chyba při GAT predikci: {pred_error}")
                    print("   ⚠️  Pokračuji bez GAT predikce...")

            else:
                print(f"   ⚠️  GAT model nenalezen: {model_path}")
                
        except Exception as e:
            print(f"   ❌ Chyba při GAT klasifikaci: {e}")
    else:
        print("\n5. 🧠 GAT klasifikace přeskočena")
    
    # Step 6: Interactive Viewer
    if interactive:
        print("\n6. 🖥️  Interaktivní zobrazení...")
        print("   🎯 Funkce:")
        print("   - Zoom kolečkem myši")
        print("   - Pan prostředním tlačítkem nebo Ctrl+drag")
        print("   - Tlačítka Zoom In/Out/Reset")
        print("   - Přepínání viditelnosti bounding boxů")
        print("   - Barevné označení klasifikací")
        print("   - Interaktivní dropdowny pro prvky s value_class > 0")
        
        # Zobrazíme statistiky před interakcí
        interactive_elements = len(df[df['value_class'] > 0])
        print(f"   📊 {interactive_elements} prvků bude mít interaktivní dropdowny")
        
        # Spustíme interaktivní viewer
        print("\n   💡 Použijte dropdown menu pro klasifikaci prvků, pak zavřete okno")
        updated_df = preview.show_document_qt(file_path, df, return_results=True)
        
        if updated_df is not None:
            df = updated_df
            print("   ✓ Výsledky z interaktivního vieweru získány")
        else:
            print("   ⚠️  Žádné výsledky z interaktivního vieweru")
    else:
        print("\n6. 🖥️  Interaktivní zobrazení přeskočeno")
    
    return df


def analyze_results(df):
    """
    Analyzuje a zobrazí statistiky výsledků.
    
    Args:
        df (pd.DataFrame): DataFrame s výsledky
    """
    print("\n7. 📊 Analýza výsledků...")
    
    if df is None or df.empty:
        print("   ⚠️  Žádná data k analýze")
        return
    
    total_elements = len(df)
    print(f"   ✓ Celkem prvků: {total_elements}")
    
    # Statistiky value_class
    if 'value_class' in df.columns:
        value_classified = len(df[df['value_class'] > 0])
        print(f"   ✓ Value klasifikované prvky: {value_classified}")
        print(f"   ✓ Value neklasifikované prvky: {total_elements - value_classified}")
    
    # Statistiky key_class
    if 'key_class' in df.columns:
        key_classified = len(df[df['key_class'] > 0])
        print(f"   ✓ Key klasifikované prvky: {key_classified}")
        print(f"   ✓ Key neklasifikované prvky: {total_elements - key_classified}")
        
        # Rozložení podle key_class
        if key_classified > 0:
            print(f"\n   🔑 Rozložení key tříd:")
            key_counts = df[df['key_class'] > 0]['key_class'].value_counts().sort_index()
            
            for class_id, count in key_counts.items():
                class_text = utils.get_key_class_name(class_id)
                print(f"      {class_id} ({class_text}): {count} prvků")
    
    # Statistiky result_class
    if 'result_class' in df.columns:
        result_classified = len(df[df['result_class'] > 0])
        print(f"   ✓ Result klasifikované prvky: {result_classified}")
        print(f"   ✓ Result neklasifikované prvky: {total_elements - result_classified}")
        
        # Rozložení podle result_class
        if result_classified > 0:
            print(f"\n   📋 Rozložení result tříd:")
            result_counts = df[df['result_class'] > 0]['result_class'].value_counts().sort_index()
            
            for class_id, count in result_counts.items():
                class_text = utils.get_result_class_name(class_id)
                print(f"      {class_id} ({class_text}): {count} prvků")
    
    # Ukázka klasifikovaných prvků
    if 'result_class' in df.columns:
        classified_sample = df[df['result_class'] > 0][['text', 'value_class', 'key_class', 'result_class']].head(5)
        if not classified_sample.empty:
            print(f"\n   🔍 Ukázka klasifikovaných prvků:")
            for _, row in classified_sample.iterrows():
                value_class_name = utils.get_value_class_name(row['value_class'])
                key_class_name = utils.get_key_class_name(row['key_class'])
                result_class_name = utils.get_result_class_name(row['result_class'])
                print(f"      '{row['text']}' → value:{value_class_name}, key:{key_class_name}, result:{result_class_name}")


def save_results(df, file_path):
    """
    Uloží výsledky do složky Results.

    Args:
        df (pd.DataFrame): DataFrame s výsledky
        file_path (str): Původní cesta k PDF souboru

    Returns:
        str: Cesta k uloženému souboru
    """
    print("\n8. 💾 Ukládání výsledků...")

    # Pokud chybí result_class sloupec, přidáme ho s nulami
    if 'result_class' not in df.columns:
        print("   ⚠️  Sloupec result_class chybí, přidávám s nulovými hodnotami")
        df = df.copy()
        df['result_class'] = 0

    try:
        output_file = utils.export_filtered_results(df, file_path)
        print(f"   ✓ Filtrované výsledky uloženy: {output_file}")
        return output_file
    except Exception as e:
        print(f"   ❌ Chyba při exportu: {e}")

        # Fallback - uložíme všechna data
        file_name = Path(file_path).stem
        output_file = f'Results/{file_name}_processed.csv'

        # Vytvoříme složku Results, pokud neexistuje
        os.makedirs('Results', exist_ok=True)

        df.to_csv(output_file, index=False)
        print(f"   💾 Všechna data uložena: {output_file}")
        return output_file


def main():
    """Hlavní funkce skriptu."""

    # ========================================
    # KONFIGURACE PRO SPUŠTĚNÍ Z IDE
    # ========================================

    # Nastavte zde parametry pro spuštění z IDE:
    IDE_MODE = True  # Změňte na False pro použití command line argumentů

    if IDE_MODE:
        # Parametry pro IDE režim - upravte podle potřeby:
        FILE_PATH = "Data/Pohoda01.pdf"          # Cesta k PDF dokumentu
        USE_GAT = True                    # True = použít GAT klasifikaci
        INTERACTIVE = True             # True = zobrazit interaktivní viewer

        print("🖥️  Spuštěno v IDE režimu")
        print(f"📄 Soubor: {FILE_PATH}")
        print(f"🧠 GAT: {'Zapnuto' if USE_GAT else 'Vypnuto'}")
        print(f"🖱️  Interaktivní: {'Zapnuto' if INTERACTIVE else 'Vypnuto'}")
        print()

        # Použijeme IDE parametry
        file_path = FILE_PATH
        use_gat = USE_GAT
        interactive = INTERACTIVE

    else:
        # Command line režim
        parser = argparse.ArgumentParser(
            description='Kompletní zpracování PDF dokumentu',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Příklady použití:
  python process_document.py Data/faktura.pdf
  python process_document.py Data/faktura.pdf --gat
  python process_document.py Data/faktura.pdf --no-interactive
  python process_document.py Data/faktura.pdf --gat --no-interactive
            """
        )

        parser.add_argument('file_path', help='Cesta k PDF dokumentu')
        parser.add_argument('--gat', action='store_true', help='Použít GAT klasifikaci')
        parser.add_argument('--no-interactive', action='store_true', help='Přeskočit interaktivní viewer')

        args = parser.parse_args()

        # Použijeme command line argumenty
        file_path = args.file_path
        use_gat = args.gat
        interactive = not args.no_interactive

    try:
        # Zpracování dokumentu
        df = process_document(
            file_path=file_path,
            use_gat=use_gat,
            interactive=interactive
        )

        # Analýza výsledků
        analyze_results(df)

        # Uložení výsledků
        output_file = save_results(df, file_path)

        print(f"\n✅ Zpracování dokončeno!")
        print(f"📁 Výsledky uloženy: {output_file}")

    except KeyboardInterrupt:
        print("\n\n⚠️  Zpracování přerušeno uživatelem")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Chyba při zpracování: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
