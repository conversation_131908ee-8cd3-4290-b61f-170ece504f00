import re

import cv2
import pytesseract
import numpy as np
import pandas as pd
from pdf2image import convert_from_path
from typing import Optional, Tuple


class OCRProcessor:
    def __init__(self, language: str = 'ces', dpi: int = 300, oem: int = 1, psm: int = 11):
        """
        Inicializuje OCR processor s nastavením Tesseractu

        :param language: Rozpoznávaný jazyk (výchozí 'ces' pro češtinu)
        :param dpi: Rozlišení pro zpracování dokumentů (výchozí 300)
        :param oem: OCR Engine Mode (výchozí 3 - LSTM + Legacy)
        :param psm: Page Segmentation Mode (výchozí 12 - Hustý text s OSD)
        """
        self._page = None
        self.dpi = dpi
        self.tesseract_config = f'--oem {oem} --psm {psm} -l {language}'

    @property
    def page(self):
        return self._page

    @page.setter
    def page(self, value):
        self._page = value

    def _binarize_image(self, image: np.ndarray) -> np.ndarray:
        """
        Provede binarizaci obrázku pomocí Otsu metody pro zlepšení čitelnosti textu.

        :param image: Vstupní obrázek jako numpy array
        :return: Binarizovaný obrázek
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        _, binarized = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return binarized

    def load_pdf(self, pdf_path: str, page_number: int = 0):
        """
        Načte stránku z PDF dokumentu a provede binarizaci

        :param pdf_path: Cesta k PDF souboru
        :param page_number: Číslo stránky (počítáno od 0)
        :raises ValueError: Pokud nelze načíst stránku z PDF
        """
        images = convert_from_path(pdf_path, dpi=self.dpi, first_page=page_number + 1, last_page=page_number + 1)
        if not images:
            raise ValueError("Nelze načítat stránku z PDF")

        self.page = self._binarize_image(np.array(images[0]))

    def load_image(self, image_path: str):
        """
        Načte obrázek a provede binarizaci

        :param image_path: Cesta k souboru s obrázkem
        :raises ValueError: Pokud nelze načíst obrázek
        """
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError("Nelze načíst obrázek")

        self.page = self._binarize_image(image)

    def get_all_text(self, image: Optional[np.ndarray] = None) -> str:
        """
        Vrátí veškerý text z dokumentu

        :param image: Volitelný vstupní obrázek, pokud není zadán, použije se aktuální stránka
        :return: Rozpoznaný text
        :raises ValueError: Pokud není zadán obrázek a není načtena žádná stránka
        """
        processing_image = self._process_input_image(image)
        return pytesseract.image_to_string(processing_image, config=self.tesseract_config)

    def get_items(self, image: Optional[np.ndarray] = None) -> pd.DataFrame:
        """
        Vrátí DataFrame s detaily rozpoznaného textu

        :param image: Volitelný vstupní obrázek, pokud není zadán, použije se aktuální stránka
        :return: DataFrame s detaily rozpoznaného textu (text, pozice, konfidence)
        :raises ValueError: Pokud není zadán obrázek a není načtena žádná stránka
        """

        self.tesseract_config = r'''
          --oem 3
          --psm 11
          --dpi 300
          -l ces+eng
        '''.replace('\n', ' ')

        processing_image = self._process_input_image(image)
        df = pytesseract.image_to_data(processing_image, output_type=pytesseract.Output.DATAFRAME, config=self.tesseract_config)
        df = df[df['conf'] > 20].reset_index(drop=True)
        df = df[df['text'] != '|'].reset_index(drop=True)
        df = df[df['text'] != ' '].reset_index(drop=True)

        # Rozdělení textů podle oddělovače
        df = self._split_by_delimiter(df, processing_image)

        text = "page"
        left = 0
        top = 0
        width = processing_image.shape[1]
        height = processing_image.shape[0]
        page = 0
        page_row = {
            'text': text,
            'left': left,
            'top': top,
            'width': width,
            'height': height,
            'conf': 100,
            'page_num': page
        }
        df = pd.concat([df, pd.DataFrame([page_row])], ignore_index=True)
        return df

    def get_text(
            self,
            bounding_box: Optional[Tuple[int, int, int, int]] = None,
            image: Optional[np.ndarray] = None,
            snippet: Optional[np.ndarray] = None
            ) -> str:
        """
        Vrátí text z konkrétní oblasti nebo snippetu

        :param bounding_box: Oblast zájmu ve formátu (x, y, šířka, výška)
        :param image: Volitelný vstupní obrázek, pokud není zadán, použije se aktuální stránka
        :param snippet: Výřez obrázku, který má být zpracován
        :return: Rozpoznaný text z dané oblasti
        :raises ValueError: Pokud není zadán obrázek a není načtena žádná stránka, nebo pokud není zadán bounding_box ani snippet
        """
        if snippet is not None:
            return pytesseract.image_to_string(self._binarize_image(snippet), config=self.tesseract_config)

        if bounding_box is None:
            raise ValueError("Musí být zadán bounding_box nebo snippet")

        processing_image = self._process_input_image(image)
        x, y, w, h = bounding_box
        roi = processing_image[y:y + h, x:x + w]
        return pytesseract.image_to_string(roi, config=self.tesseract_config)

    def _process_input_image(self, image: Optional[np.ndarray]) -> np.ndarray:
        """
        Zpracuje vstupní obrázek pro OCR

        :param image: Volitelný vstupní obrázek, pokud není zadán, použije se aktuální stránka
        :return: Zpracovaný obrázek připravený pro OCR
        :raises ValueError: Pokud není zadán obrázek a není načtena žádná stránka
        """
        if image is not None:
            return self._binarize_image(image)

        if self.page is None:
            raise ValueError("Není načten žádný dokument")

        return self.page

    def load_pdf_pages(self, pdf_path: str, start_page: int = 0, end_page: Optional[int] = None) -> list:
        """
        Načte více stránek z PDF dokumentu a vrátí seznam binarizovaných obrázků

        :param pdf_path: Cesta k PDF souboru
        :param start_page: Počáteční stránka (počítáno od 0)
        :param end_page: Koncová stránka (včetně), pokud None, načtou se všechny stránky od start_page
        :return: Seznam binarizovaných obrázků
        :raises ValueError: Pokud nelze načíst stránky z PDF
        """
        # Konverze na číslování od 1 pro pdf2image
        first_page = start_page + 1
        last_page = None if end_page is None else end_page + 1

        try:
            images = convert_from_path(pdf_path, dpi=self.dpi, first_page=first_page, last_page=last_page)
            if not images:
                raise ValueError(f"Nelze načítat stránky {start_page}-{end_page} z PDF")

            # Binarizace všech stránek
            binarized_pages = [self._binarize_image(np.array(img)) for img in images]

            # Nastavíme první stránku jako aktuální
            if binarized_pages:
                self.page = binarized_pages[0]

            return binarized_pages
        except Exception as e:
            raise ValueError(f"Chyba při načítání PDF: {str(e)}")

    def save_processed_image(self, output_path: str, image: Optional[np.ndarray] = None):
        """
        Uloží zpracovaný (binarizovaný) obrázek na disk

        :param output_path: Cesta pro uložení obrázku
        :param image: Volitelný vstupní obrázek, pokud není zadán, použije se aktuální stránka
        :raises ValueError: Pokud není zadán obrázek a není načtena žádná stránka
        """
        processing_image = self._process_input_image(image)
        cv2.imwrite(output_path, processing_image)

    def get_boxes(self, image: Optional[np.ndarray] = None) -> pd.DataFrame:
        """
        Vrátí DataFrame s bounding boxy jednotlivých znaků pomocí Tesseract image_to_boxes.

        Na rozdíl od get_items(), která vrací word-level elementy, tato metoda poskytuje
        character-level detekci s přesnými pozicemi každého znaku.

        :param image: Volitelný vstupní obrázek, pokud není zadán, použije se aktuální stránka
        :return: DataFrame s bounding boxy znaků obsahující sloupce:
                - char: rozpoznaný znak
                - left: X souřadnice levého okraje
                - top: Y souřadnice horního okraje (převedeno na standardní souřadnice)
                - right: X souřadnice pravého okraje
                - bottom: Y souřadnice dolního okraje (převedeno na standardní souřadnice)
                - width: šířka bounding boxu
                - height: výška bounding boxu
                - page: číslo stránky
        :raises ValueError: Pokud není zadán obrázek a není načtena žádná stránka

        Příklad použití:
            ocr = OCRProcessor()
            ocr.load_pdf('document.pdf')
            boxes_df = ocr.get_boxes()
            print(f"Nalezeno {len(boxes_df)} znaků")
        """
        processing_image = self._process_input_image(image)

        self.tesseract_config = r'''
          --oem 1
          --psm 11
          --dpi 300
          -c preserve_interword_spaces=1
          -c textord_tabfind_vertical_text=0
          -c textord_tabfind_force_vertical_splits=0
          -c textord_detect_tables=0
          -c textord_find_heavy_artifacts=0
          -c textord_noise_norm=1
          -c textord_min_lines=1
          -c textord_min_xheight=10
        '''.replace('\n', ' ')

        # Získání box dat z Tesseractu
        box_data = pytesseract.image_to_boxes(processing_image, config=self.tesseract_config)

        # Parsování výstupu do strukturovaného formátu
        boxes = []
        image_height = processing_image.shape[0]

        for line in box_data.strip().split('\n'):
            if line.strip():
                parts = line.split()
                if len(parts) >= 6:
                    char = parts[0]
                    left = int(parts[1])
                    bottom = int(parts[2])
                    right = int(parts[3])
                    top = int(parts[4])
                    page = int(parts[5])

                    # Převod souřadnic - Tesseract používá souřadnice s počátkem vlevo dole
                    # Převedeme na standardní souřadnice s počátkem vlevo nahoře
                    converted_top = image_height - top
                    converted_bottom = image_height - bottom

                    boxes.append({
                        'char': char,
                        'left': left,
                        'top': converted_top,
                        'right': right,
                        'bottom': converted_bottom,
                        'width': right - left,
                        'height': converted_bottom - converted_top,
                        'page': page
                    })

        # Vytvoření DataFrame
        df = pd.DataFrame(boxes)

        # Filtrování prázdných nebo neplatných boxů
        if not df.empty:
            df = df[df['width'] > 0]
            df = df[df['height'] > 0]
            df = df.reset_index(drop=True)

        return df

    def _split_by_delimiter(self, df: pd.DataFrame, image: np.ndarray, delimiter: str = ":|") -> pd.DataFrame:
        """
        Rozdělí texty obsahující oddělovač na dva samostatné tokeny.

        :param df: DataFrame s OCR výsledky
        :param image: Zpracovaný obrázek pro OCR
        :param delimiter: Oddělovač pro rozdělení textu (výchozí ":|")
        :return: DataFrame s rozdělenými texty
        """
        if df.empty:
            return df

        # Najdeme řádky obsahující oddělovač uprostřed textu
        # Nejprve zkontrolujeme, jaké texty máme v DataFrame
        print(f"DEBUG: Hledám oddělovač '{delimiter}' v {len(df)} textech")

        # Vytvoříme masku pro texty obsahující oddělovač
        delimiter_mask = df['text'].astype(str).str.contains(re.escape(delimiter), na=False)

        # Debug výpis
        texts_with_delimiter = df[delimiter_mask]['text'].tolist()
        if texts_with_delimiter:
            print(f"DEBUG: Nalezeny texty s oddělovačem: {texts_with_delimiter}")
        else:
            print("DEBUG: Žádné texty s oddělovačem nenalezeny")
            # Zkusíme najít texty obsahující ':' nebo '|' samostatně
            colon_mask = df['text'].astype(str).str.contains(':', na=False)
            pipe_mask = df['text'].astype(str).str.contains('\\|', na=False)
            colon_texts = df[colon_mask]['text'].tolist()
            pipe_texts = df[pipe_mask]['text'].tolist()
            if colon_texts:
                print(f"DEBUG: Texty s ':' : {colon_texts[:5]}")  # Prvních 5
            if pipe_texts:
                print(f"DEBUG: Texty s '|' : {pipe_texts[:5]}")  # Prvních 5

        rows_to_split = df[delimiter_mask].copy()

        if rows_to_split.empty:
            return df

        # Seznam nových řádků pro přidání
        new_rows = []
        # Indexy řádků k odstranění
        rows_to_remove = []

        for idx, row in rows_to_split.iterrows():
            text = row['text']

            # Kontrola, že oddělovač není na začátku nebo konci
            if text.startswith(delimiter) or text.endswith(delimiter):
                continue

            # Rozdělení textu podle oddělovače
            parts = text.split(delimiter, 1)  # Rozdělíme pouze na první výskyt
            if len(parts) != 2:
                continue

            before_delimiter = parts[0].strip()
            after_delimiter = parts[1].strip()

            # Pokud některá část je prázdná, přeskočíme
            if not before_delimiter or not after_delimiter:
                continue

            try:
                # Znovu načteme oblast pomocí OCR s parametrem -psm 7 pro jednotlivé znaky
                bbox = (int(row['left']), int(row['top']), int(row['width']), int(row['height']))
                char_level_text = self._get_character_level_text(bbox, image)

                if not char_level_text:
                    continue

                # Najdeme pozici oddělovače v character-level textu
                delimiter_pos = char_level_text.find(delimiter)
                if delimiter_pos == -1:
                    continue

                # Rozdělíme znaky před a za oddělovačem
                chars_before = char_level_text[:delimiter_pos]
                chars_after = char_level_text[delimiter_pos + len(delimiter):]

                # Vytvoříme nové řádky pro oba tokeny
                # První token (před oddělovačem)
                new_row_before = row.copy()
                new_row_before['text'] = chars_before.strip()

                # Druhý token (za oddělovačem) - přibližně umístíme vpravo
                new_row_after = row.copy()
                new_row_after['text'] = chars_after.strip()
                # Posuneme left souřadnici přibližně doprava
                estimated_char_width = row['width'] / len(text) if len(text) > 0 else 10
                new_row_after['left'] = row['left'] + len(chars_before + delimiter) * estimated_char_width
                new_row_after['width'] = row['width'] - len(chars_before + delimiter) * estimated_char_width

                # Přidáme nové řádky pouze pokud obsahují text
                if new_row_before['text']:
                    new_rows.append(new_row_before)
                if new_row_after['text']:
                    new_rows.append(new_row_after)

                # Označíme původní řádek k odstranění
                rows_to_remove.append(idx)

            except Exception as e:
                # Pokud se nepodaří zpracovat, ponecháme původní řádek
                print(f"Chyba při rozdělování textu '{text}': {e}")
                continue

        # Odstraníme původní řádky s oddělovačem
        if rows_to_remove:
            df = df.drop(rows_to_remove)

        # Přidáme nové řádky
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            df = pd.concat([df, new_df], ignore_index=True)

        return df

    def _get_character_level_text(self, bbox: Tuple[int, int, int, int], image: np.ndarray) -> str:
        """
        Získá text z oblasti pomocí OCR s parametrem -psm 7 pro jednotlivé znaky.

        :param bbox: Bounding box ve formátu (x, y, width, height)
        :param image: Zpracovaný obrázek
        :return: Text rozpoznaný na úrovni jednotlivých znaků
        """
        try:
            x, y, w, h = bbox

            # Výřez oblasti z obrázku
            roi = image[y:y + h, x:x + w]

            # Konfigurace pro single text line s character-level rozpoznáváním
            char_config = r'''
              --oem 1
              --psm 7
              --dpi 300
              -l ces+eng
              -c preserve_interword_spaces=1
            '''.replace('\n', ' ')

            # OCR na výřezu
            text = pytesseract.image_to_string(roi, config=char_config)
            return text.strip()

        except Exception as e:
            print(f"Chyba při character-level OCR: {e}")
            return ""

