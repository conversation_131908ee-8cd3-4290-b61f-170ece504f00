#!/usr/bin/env python3
"""
Skript pro porovnání GAT výsledků mezi:
1. Anotovanými daty z CSV souborů (Tests/)
2. <PERSON><PERSON> zpracovanými přímo z PDF (celá pipeline)

Tento skript odhalí rozdíly ve vstupních datech pro GAT síť.
"""

import sys
import os
import pandas as pd
import torch
import numpy as np

# Přidáme cesty pro importy
sys.path.append('.')
sys.path.append('GAT')

from GAT.GATClassifier import GatNodeClassifier
from GAT.prepare_graph_data import prepare_graph_data_from_dataframe
from GAT.train_gat import prepare_spatial_features, prepare_class_features
from utils import utils
from OCR import ocr
from semantic_classifier import key_classify

def load_csv_data(csv_path):
    """Načte anotovaná data z CSV souboru."""
    if not os.path.exists(csv_path):
        print(f"❌ Soubor {csv_path} neexistuje")
        return None
    
    df = pd.read_csv(csv_path)
    print(f"✓ Načten CSV: {csv_path} ({len(df)} řádků)")
    return df

def process_pdf_full_pipeline(pdf_path):
    """Zpracuje PDF celou pipeline stejně jako process_document.py."""
    if not os.path.exists(pdf_path):
        print(f"❌ Soubor {pdf_path} neexistuje")
        return None
    
    print(f"🔍 Zpracovávám PDF: {pdf_path}")
    
    try:
        # 1. OCR zpracování
        print("   1. OCR zpracování...")
        df = ocr.do(pdf_path)
        print(f"      ✓ Nalezeno {len(df)} textových prvků")
        
        # 2. Zpracování textů
        print("   2. Zpracování textů...")
        df = utils.merge_texts(df)
        print(f"      ✓ Po sloučení: {len(df)} prvků")
        
        utils.classify_batch_values(df)
        value_classified = len(df[df['value_class'] > 0])
        print(f"      ✓ Klasifikováno {value_classified}/{len(df)} hodnot")
        
        df = utils.clean_texts(df)
        print("      ✓ Texty vyčištěny")
        
        # 3. Klasifikace klíčů
        print("   3. Klasifikace klíčů...")
        df = key_classify.do(df)
        key_classified = len(df[df['key_class'] > 0])
        print(f"      ✓ Klasifikováno {key_classified}/{len(df)} klíčů")
        
        # 4. Postprocessing
        print("   4. Postprocessing...")
        df = utils.postprocess(df)
        df = utils.remove_invalid_texts(df)
        print(f"      ✓ Finální počet prvků: {len(df)}")
        
        return df
        
    except Exception as e:
        print(f"   ❌ Chyba při zpracování PDF: {e}")
        return None

def prepare_data_for_gat(df, source_name):
    """Připraví data pro GAT klasifikaci."""
    print(f"   Příprava dat pro GAT ({source_name})...")
    
    # Ujistíme se, že result_class existuje
    if 'result_class' not in df.columns:
        df['result_class'] = 0
    
    df_prepared = prepare_spatial_features(df.copy())
    df_prepared, feature_names = prepare_class_features(df_prepared)
    
    print(f"      ✓ Připraveno {len(df_prepared)} prvků")
    return df_prepared

def run_gat_classification(df, device, classifier, source_name):
    """Spustí GAT klasifikaci na datech."""
    print(f"   GAT klasifikace ({source_name})...")
    
    try:
        # Detekce anotací
        has_annotations = 'result_class' in df.columns and len(df[df['result_class'] > 0]) > 0
        
        node_features, edge_features, adj, labels, test_mask = prepare_graph_data_from_dataframe(
            df, device, is_training=has_annotations
        )
        
        with torch.no_grad():
            outputs = classifier(node_features, edge_features, adj)
            predictions = torch.argmax(outputs, dim=-1).squeeze(0).cpu().numpy()
        
        # Aplikujeme predikce na DataFrame
        df_result = df.copy()
        for idx in range(len(predictions)):
            if idx < len(df_result):
                df_result.iloc[idx, df_result.columns.get_loc('result_class')] = predictions[idx]
        
        print(f"      ✓ Klasifikace dokončena")
        return df_result, predictions, labels.cpu().numpy() if has_annotations else None, test_mask.cpu().numpy() if has_annotations else None
        
    except Exception as e:
        print(f"      ❌ Chyba při GAT klasifikaci: {e}")
        return None, None, None, None

def compare_dataframes(df_csv, df_pdf, csv_name, pdf_name):
    """Porovná dva DataFrames a najde rozdíly."""
    print(f"\n🔍 Porovnání dat: {csv_name} vs {pdf_name}")
    print("-" * 50)
    
    print(f"   Počet řádků: {len(df_csv)} vs {len(df_pdf)}")
    
    # Porovnání textů
    csv_texts = set(df_csv['text'].tolist())
    pdf_texts = set(df_pdf['text'].tolist())
    
    common_texts = csv_texts & pdf_texts
    csv_only = csv_texts - pdf_texts
    pdf_only = pdf_texts - csv_texts
    
    print(f"   Společné texty: {len(common_texts)}")
    print(f"   Pouze v CSV: {len(csv_only)}")
    print(f"   Pouze v PDF: {len(pdf_only)}")
    
    if csv_only:
        print("   📝 Texty pouze v CSV (ukázka):")
        for text in list(csv_only)[:5]:
            print(f"      '{text}'")
    
    if pdf_only:
        print("   📝 Texty pouze v PDF (ukázka):")
        for text in list(pdf_only)[:5]:
            print(f"      '{text}'")
    
    # Porovnání klasifikací pro společné texty
    if common_texts:
        print(f"\n   🔍 Porovnání klasifikací pro společné texty:")
        
        differences = []
        for text in list(common_texts)[:10]:  # Omezíme na 10 pro přehlednost
            csv_row = df_csv[df_csv['text'] == text].iloc[0]
            pdf_row = df_pdf[df_pdf['text'] == text].iloc[0]
            
            csv_value = csv_row.get('value_class', 0)
            pdf_value = pdf_row.get('value_class', 0)
            csv_key = csv_row.get('key_class', 0)
            pdf_key = pdf_row.get('key_class', 0)
            
            if csv_value != pdf_value or csv_key != pdf_key:
                differences.append({
                    'text': text,
                    'csv_value': csv_value,
                    'pdf_value': pdf_value,
                    'csv_key': csv_key,
                    'pdf_key': pdf_key
                })
        
        if differences:
            print(f"      ⚠️  Nalezeno {len(differences)} rozdílů v klasifikaci:")
            for diff in differences[:5]:
                print(f"         '{diff['text'][:20]}...' → value: {diff['csv_value']}→{diff['pdf_value']}, key: {diff['csv_key']}→{diff['pdf_key']}")
        else:
            print("      ✅ Klasifikace jsou identické pro společné texty")

def main():
    """Hlavní funkce pro porovnání."""
    print("🔍 Porovnání GAT výsledků: CSV vs PDF Pipeline")
    print("=" * 60)
    
    # Konfigurace - můžete změnit podle potřeby
    test_files = [
        ("Tests/Faktura.csv", "Data/Faktura.pdf"),
        ("Tests/Pohoda07.csv", "Data/Pohoda07.pdf")
    ]
    
    # Načtení GAT modelu
    print("\n📋 Načítání GAT modelu...")
    device = torch.device('cpu')
    
    classifier = GatNodeClassifier(
        node_in_dim=38,
        edge_in_dim=2,
        hidden_dim=64,
        heads=4,
        output_dim=19,
        num_layers=1
    ).to(device)
    
    model_path = "GAT/trained_gat_model_new.pth"
    classifier.load_state_dict(torch.load(model_path, map_location=device))
    classifier.eval()
    print("   ✅ Model načten")
    
    # Zpracování každého páru souborů
    for csv_path, pdf_path in test_files:
        print(f"\n{'='*60}")
        print(f"📄 Zpracovávám pár: {os.path.basename(csv_path)} vs {os.path.basename(pdf_path)}")
        print(f"{'='*60}")
        
        # 1. Načtení CSV dat
        print("\n📋 1. Načítání anotovaných dat z CSV...")
        df_csv = load_csv_data(csv_path)
        if df_csv is None:
            continue
        
        # 2. Zpracování PDF
        print("\n📋 2. Zpracování PDF celou pipeline...")
        df_pdf = process_pdf_full_pipeline(pdf_path)
        if df_pdf is None:
            continue
        
        # 3. Porovnání vstupních dat
        compare_dataframes(df_csv, df_pdf, "CSV", "PDF")
        
        # 4. Příprava dat pro GAT
        print("\n📋 3. Příprava dat pro GAT...")
        df_csv_prepared = prepare_data_for_gat(df_csv, "CSV")
        df_pdf_prepared = prepare_data_for_gat(df_pdf, "PDF")
        
        # 5. GAT klasifikace
        print("\n📋 4. GAT klasifikace...")
        df_csv_result, pred_csv, labels_csv, mask_csv = run_gat_classification(
            df_csv_prepared, device, classifier, "CSV"
        )
        df_pdf_result, pred_pdf, labels_pdf, mask_pdf = run_gat_classification(
            df_pdf_prepared, device, classifier, "PDF"
        )
        
        if df_csv_result is not None and df_pdf_result is not None:
            # 6. Porovnání GAT výsledků
            print("\n📋 5. Porovnání GAT výsledků...")
            
            # Najdeme společné texty a porovnáme jejich predikce
            csv_texts = df_csv_result['text'].tolist()
            pdf_texts = df_pdf_result['text'].tolist()
            
            print("   🔍 Porovnání predikcí pro společné texty:")
            differences = 0
            comparisons = 0
            
            for i, csv_text in enumerate(csv_texts[:20]):  # Omezíme na 20
                if csv_text in pdf_texts:
                    pdf_idx = pdf_texts.index(csv_text)
                    
                    csv_pred = df_csv_result.iloc[i]['result_class']
                    pdf_pred = df_pdf_result.iloc[pdf_idx]['result_class']
                    
                    if csv_pred != pdf_pred:
                        differences += 1
                        csv_name = utils.get_result_class_name(csv_pred)
                        pdf_name = utils.get_result_class_name(pdf_pred)
                        print(f"      ❌ '{csv_text[:20]}...' → CSV: {csv_pred} ({csv_name}) | PDF: {pdf_pred} ({pdf_name})")
                    
                    comparisons += 1
            
            if differences == 0:
                print(f"      ✅ Všechny predikce jsou identické! ({comparisons} porovnání)")
            else:
                print(f"      ⚠️  Nalezeno {differences} rozdílů z {comparisons} porovnání ({differences/comparisons*100:.1f}%)")
    
    print("\n✅ Porovnání dokončeno!")

if __name__ == "__main__":
    main()
